<template>
  <div class="page-container">
    <VerticalTabNav />

    <div class="list-page">
      <!-- 搜索和操作区域 -->
      <div class="filter-area">
        <div class="search-area">
          <el-input v-model="searchValue" placeholder="请输入车牌号搜索" clearable class="search-input" @keyup.enter="handleQuery"></el-input>
        </div>
        <div class="operation-area">
          <el-button class="custom-button primary-button" @click="handleAdd">
            <el-icon>
              <Plus />
            </el-icon>
            新增工程车辆
          </el-button>
          <el-button type="danger" icon="Delete" :disabled="selectedRows.length === 0" @click="handleBatchDelete" class="custom-button">
            删除
          </el-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        height="calc(100vh - 250px)"
        :header-cell-style="{ background: 'var(--el-bg-color-page)' }"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" type="index" width="80" align="center" />
        <el-table-column label="车牌号" prop="vehicleNo" min-width="120" />
        <el-table-column label="车辆类型" prop="vehicleType" width="120" align="center" />
        <el-table-column label="所属执法机构" prop="agencyName" min-width="150" />
        <el-table-column label="所属部门" prop="deptName" min-width="150" />
        <el-table-column label="重点保障桥梁" prop="bridgeName" min-width="150" />
        <el-table-column label="车辆型号" prop="vehicleModel" width="120" align="center" />
        <el-table-column label="购置日期" prop="purchaseDate" width="120" align="center">
          <template #default="scope">
            {{ formatDate(scope.row.purchaseDate) }}
          </template>
        </el-table-column>
        <el-table-column label="车辆状态" prop="status" width="120" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.status === 1">待命</el-tag>
            <el-tag v-else-if="scope.row.status === 2" type="warning">执行任务</el-tag>
            <el-tag v-else-if="scope.row.status === 3" type="danger">维修</el-tag>
            <el-tag v-else-if="scope.row.status === 4" type="info">报废</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="当前位置" prop="currentPosition" min-width="150" />
        <el-table-column label="上次保养日期" prop="lastMaintainDate" width="120" align="center">
          <template #default="scope">
            {{ formatDate(scope.row.lastMaintainDate) }}
          </template>
        </el-table-column>
        <el-table-column label="车辆负责人" prop="responsiblePerson" width="120" align="center" />
        <el-table-column label="联系电话" prop="contactPhone" width="120" align="center" />
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 15, 20, 25]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 编辑/新增弹窗 -->
      <el-dialog v-model="dialogVisible" :title="dialogType === 'add' ? '新增工程车辆' : '编辑工程车辆'" width="60%" top="5vh" destroy-on-close>
        <el-form ref="formRef" :model="form" :rules="rules" label-width="140px" class="dialog-form">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="车牌号" prop="vehicleNo">
  <el-select v-model="form.vehicleNo" placeholder="请选择车牌号">
    <el-option v-for="item in vehicleOptions" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="车辆类型" prop="vehicleType">
                <el-select v-model="form.vehicleType" placeholder="请选择车辆类型" style="width: 100%">
                  <el-option label="工程车" value="工程车" />
                  <el-option label="巡逻车" value="巡逻车" />
                  <el-option label="救援车" value="救援车" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="所属执法机构" prop="agencyId">
                <el-select v-model="form.agencyId" placeholder="请选择所属执法机构" style="width: 100%">
                  <el-option
                    v-for="item in agencyOptions"
                    :key="item.agencyId"
                    :label="item.agencyName"
                    :value="item.agencyId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属部门" prop="deptId">
                <el-select v-model="form.deptId" placeholder="请选择所属部门" style="width: 100%">
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.deptId"
                    :label="item.deptName"
                    :value="item.deptId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="重点保障桥梁" prop="bridgeId">
                <el-select v-model="form.bridgeId" placeholder="请选择重点保障桥梁" style="width: 100%">
                  <el-option
                    v-for="item in bridgeOptions"
                    :key="item.bridgeId"
                    :label="item.bridgeName"
                    :value="item.bridgeId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="车辆型号" prop="vehicleModel">
                <el-input v-model="form.vehicleModel" placeholder="请输入车辆型号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="购置日期" prop="purchaseDate">
                <el-date-picker v-model="form.purchaseDate" type="date" placeholder="选择购置日期" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="车辆状态" prop="status">
                <el-select v-model="form.status" placeholder="请选择车辆状态" style="width: 100%">
                  <el-option label="待命" :value="1" />
                  <el-option label="执行任务" :value="2" />
                  <el-option label="维修" :value="3" />
                  <el-option label="报废" :value="4" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="当前位置" prop="currentPosition">
                <el-input v-model="form.currentPosition" placeholder="请输入当前位置" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="上次保养日期" prop="lastMaintainDate">
                <el-date-picker v-model="form.lastMaintainDate" type="date" placeholder="选择上次保养日期" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="车辆负责人" prop="responsiblePerson">
                <el-input v-model="form.responsiblePerson" placeholder="请输入车辆负责人" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="联系电话" prop="contactPhone">
                <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="随车装备配置" prop="equipmentConfig">
                <el-input
                  v-model="form.equipmentConfig"
                  type="textarea"
                  placeholder="请输入随车装备配置"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleSubmit">确定</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import VerticalTabNav from './components/VerticalTabNav.vue'
import {
  getEngineeringVehicleList,
  addEngineeringVehicle,
  updateEngineeringVehicle,
  deleteEngineeringVehicle,
  getLawEnforcementAgencyList
} from '@/api/bridge/bisc/engineering-vehicle'
import { listDept } from '@/api/system/dept/index'
import { getBridgeList } from '@/api/bridge/bisc/bridge';
import { getDangerousGoodsCarInfo } from '@/api/bridge/bisc/car';

// 定义数据
const searchValue = ref('')
const loading = ref(false)
const selectedRows = ref([])
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const dialogVisible = ref(false)
const dialogType = ref('add')

// 格式化日期
const formatDate = (date: string | number | Date) => {
  if (!date) return '-';
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};


// 表单相关
const formRef = ref()
const form = reactive({
  vehicleId: null,
  vehicleNo: '',
  vehicleType: '',
  agencyId: null,
  deptId: null,
  bridgeId: null,
  vehicleModel: '',
  purchaseDate: '',
  status: 1,
  currentPosition: '',
  lastMaintainDate: '',
  responsiblePerson: '',
  contactPhone: '',
  equipmentConfig: ''
})

// 机构、部门和桥梁选项
const agencyOptions = ref([])
const deptOptions = ref([])
const bridgeOptions = ref([])
const vehicleOptions = ref([])

// 表单验证规则
const rules = {
  vehicleNo: [{ required: true, message: '请输入车牌号', trigger: 'blur' }],
  vehicleType: [{ required: true, message: '请选择车辆类型', trigger: 'change' }],
  agencyId: [{ required: true, message: '请选择所属执法机构', trigger: 'change' }],
  deptId: [{ required: true, message: '请选择所属部门', trigger: 'change' }],
  status: [{ required: true, message: '请选择车辆状态', trigger: 'change' }]
}

// 获取表格数据
const getTableData = async () => {
  loading.value = true
  try {
      const res = await getEngineeringVehicleList({
        page: currentPage.value,
        size: pageSize.value,
        search: searchValue.value
      })
      if (res.code === 200) {
        // 处理所属部门、执法机构和桥梁名称的映射
        const formattedRows = res.data.rows.map(item => ({
          ...item,
          // 所属部门名称
          deptName: deptOptions.value.find(dept => dept.id === item.createDept)?.deptName || '未知部门',
          // 所属执法机构名称
          agencyName: agencyOptions.value.find(agency => agency.id === item.agencyId)?.agencyName || '未知机构',
          // 重点保障桥梁名称
          bridgeName: bridgeOptions.value.find(bridge => bridge.id === item.bridgeId)?.bridgeName || '未知桥梁'
        }));
        tableData.value = formattedRows;
        total.value = res.data.total;
      }
    } catch (error) {
      console.error('获取工程车辆列表失败:', error);
      ElMessage.error('获取数据失败，请重试');
    } finally {
    loading.value = false
  }
}

// 获取机构选项
const getAgencyOptions = async () => {
  try {
    const res = await listDept()
    console.log('机构数据:', res); // 调试日志
    // 根据实际返回的数据结构调整
    const data = res.data || res.rows || res;
    agencyOptions.value = Array.isArray(data) ? data.map(item => ({
      agencyId: item.deptId || item.id,
      agencyName: item.deptName
    })) : [];
  } catch (error) {
    console.error('获取机构数据失败:', error)
    ElMessage.error('获取机构数据失败')
  }
}

// 获取部门选项
const getDeptOptions = async () => {
  try {
    const res = await listDept()
    console.log('部门数据:', res); // 调试日志
    // 根据实际返回的数据结构调整
    const data = res.data || res.rows || res;
    deptOptions.value = Array.isArray(data) ? data.map(item => ({
      deptId: item.deptId || item.id,
      deptName: item.deptName
    })) : [];
  } catch (error) {
    console.error('获取部门数据失败:', error)
    ElMessage.error('获取部门数据失败')
  }
}

// 获取桥梁选项
const queryParams = ref({
  pageNum: 1,
  pageSize: 1000000
});

const getBridgeOptions = async () => {
  try {
    const res = await getBridgeList(queryParams.value);
    console.log('桥梁数据:', res); // 调试日志
    // 根据实际返回的数据结构调整
    const data = res.rows || res.data || res;
    bridgeOptions.value = Array.isArray(data) ? data.map(item => ({
      bridgeId: item.id || item.bridgeId,
      bridgeName: item.bridgeName
    })) : [];
  } catch (error) {
    console.error("获取桥梁列表失败:", error);
    ElMessage.error("获取桥梁列表失败");
  }
};

// 获取车牌号选项
const getVehicleOptions = async () => {
  try {
    const res = await getDangerousGoodsCarInfo({ pageNum: 1, pageSize: 1000000 });
    console.log('车牌号数据:', res); // 调试日志
    // 根据实际返回的数据结构调整
    const data = res.rows || res.data || res;
    vehicleOptions.value = Array.isArray(data) ? data.map(item => ({
     value: item.id,
      label: item.licensePlate || '未知车辆',
    })) : [];
  } catch (error) {
    console.error("获取车牌号数据失败:", error);
    ElMessage.error("获取车牌号数据失败");
  }
};

// 搜索
const handleQuery = () => {
  currentPage.value = 1
  getTableData()
}

// 重置
const handleReset = () => {
  searchValue.value = ''
  currentPage.value = 1
  getTableData()
}

// 添加
const handleAdd = () => {
  dialogType.value = 'add'
  dialogVisible.value = true
  // 重置表单
  Object.assign(form, {
    vehicleId: null,
    vehicleNo: '',
    vehicleType: '',
    agencyId: null,
    deptId: null,
    bridgeId: null,
    vehicleModel: '',
    purchaseDate: '',
    status: 1,
    currentPosition: '',
    lastMaintainDate: '',
    responsiblePerson: '',
    contactPhone: '',
    equipmentConfig: ''
  })
}

// 编辑
const handleEdit = (row: any) => {
  dialogType.value = 'edit'
  dialogVisible.value = true
  // 填充表单数据
  Object.assign(form, {
    vehicleId: row.vehicleId,
    vehicleNo: row.vehicleNo,
    vehicleType: row.vehicleType,
    agencyId: row.agencyId,
    deptId: row.agencyId,
    bridgeId: row.bridgeId,
    vehicleModel: row.vehicleModel,
    purchaseDate: row.purchaseDate,
    status: row.status,
    currentPosition: row.currentPosition,
    lastMaintainDate: row.lastMaintainDate,
    responsiblePerson: row.responsiblePerson,
    contactPhone: row.contactPhone,
    equipmentConfig: row.equipmentConfig
  })
}

// 删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除该工程车辆信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteEngineeringVehicle(row.vehicleId)
      ElMessage.success('删除成功')
      getTableData()
    } catch (error) {
      console.error(error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的数据')
    return
  }

  ElMessageBox.confirm('确定要批量删除选中的工程车辆信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 批量删除需要修改后端接口支持，这里暂时逐个删除
      const deletePromises = selectedRows.value.map((item: any) =>
        deleteEngineeringVehicle(item.vehicleId)
      )
      await Promise.all(deletePromises)
      ElMessage.success('批量删除成功')
      getTableData()
    } catch (error) {
      console.error(error)
      ElMessage.error('批量删除失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 提交表单
const handleSubmit = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        if (dialogType.value === 'add') {
          await addEngineeringVehicle(form)
          ElMessage.success('新增成功')
        } else {
          await updateEngineeringVehicle(form)
          ElMessage.success('更新成功')
        }
        dialogVisible.value = false
        getTableData()
      } catch (error) {
        console.error(error)
        ElMessage.error('操作失败')
      }
    }
  })
}

// 处理分页
const handleSizeChange = (val: number) => {
  pageSize.value = val
  getTableData()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  getTableData()
}

// 处理选择变化
const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows
}

// 初始化
onMounted(async () => {
  // 先加载所有选项数据，再查询列表
  await Promise.all([
    getAgencyOptions(),
    getDeptOptions(),
    getBridgeOptions(),
    getVehicleOptions()
  ]);
  getTableData();
})
</script>

<style scoped>
.page-container {
  display: flex;
  height: 100%;
}

.list-page {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.filter-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: var(--el-bg-color-overlay);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
}

.search-area {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-input {
  width: 300px;
}

.operation-area {
  display: flex;
  gap: 12px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.dialog-form {
  padding: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 20px 20px;
  gap: 12px;
}
</style>
